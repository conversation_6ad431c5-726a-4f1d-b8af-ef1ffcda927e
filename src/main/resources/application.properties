spring.application.name=AI Commons

logging.level.com.enttribe.commons.ai=DEBUG

#prompt.service.url=http://localhost:8081/prompt-analyzer/rest
#prompt.service.url=https://dev.visionwaves.com/promptsmith

commons.ai.sdk.is_local=true
commons.ai.sdk.app.name=EMAIL_ASSISTANT_APP_NAME
commons.ai.sdk.default.llm.model=llama-3.3-70b-versatile
commons.ai.sdk.prompt.refresh.interval=100000000


#commons.ai.sdk.vector_store.config=[{"ssl":{"enabled":"true"},"vectorDatabase":"redis","vector_store_id":"1","inference":"huggingface","embeddingModel":"BAAI/bge-base-en-v1.5","host":"localhost","port":6379,"username":"","password":"","initializeSchema":true,"indexName":"spring","prefix":"abc"}]
commons.ai.sdk.vector_store.config=W3sic3NsIjp7ImVuYWJsZWQiOiJ0cnVlIn0sInZlY3RvckRhdGFiYXNlIjoicmVkaXMiLCJ2ZWN0b3Jfc3RvcmVfaWQiOiJhYmMiLCJpbmZlcmVuY2UiOiJodWdnaW5nZmFjZSIsImVtYmVkZGluZ01vZGVsIjoiQkFBSS9iZ2UtYmFzZS1lbi12MS41Iiwic2VudGluZWwiOnsibWFzdGVyIjoibXltYXN0ZXIiLCJub2RlcyI6WyJyZWRpcy1ub2RlLTAucmVkaXMtaGVhZGxlc3MudmVjdG9yLXJlZGlzLnN2Yy5jbHVzdGVyLmxvY2FsOjI2Mzc5IiwicmVkaXMtbm9kZS0xLnJlZGlzLWhlYWRsZXNzLnZlY3Rvci1yZWRpcy5zdmMuY2x1c3Rlci5sb2NhbDoyNjM3OSIsInJlZGlzLW5vZGUtMi5yZWRpcy1oZWFkbGVzcy52ZWN0b3ItcmVkaXMuc3ZjLmNsdXN0ZXIubG9jYWw6MjYzNzkiXX0sInVzZXJuYW1lIjoiIiwicGFzc3dvcmQiOiJWZWN0b3IjMTIxQGRlbW8iLCJpbml0aWFsaXplU2NoZW1hIjp0cnVlLCJpbmRleE5hbWUiOiJ0ZXN0aW5nIiwicHJlZml4IjoidGVzdGluZy0iLCJtZXRhZGF0YUZpZWxkcyI6W3sibmFtZSI6ImVudGl0eV9uYW1lIiwiZmllbGRUeXBlIjoiVEFHIn0seyJuYW1lIjoiZW50aXR5X2lkIiwiZmllbGRUeXBlIjoiTlVNRVJJQyJ9LHsibmFtZSI6ImludGVudF9uYW1lIiwiZmllbGRUeXBlIjoiVEFHIn0seyJuYW1lIjoiaW50ZW50X2lkIiwiZmllbGRUeXBlIjoiTlVNRVJJQyJ9LHsibmFtZSI6InNlbWFudGljX3JvdXRlcl9pZCIsImZpZWxkVHlwZSI6Ik5VTUVSSUMifSx7Im5hbWUiOiJtaWNyb19pbnRlbnRfaWQiLCJmaWVsZFR5cGUiOiJOVU1FUklDIn0seyJuYW1lIjoidG9vbCIsImZpZWxkVHlwZSI6IlRBRyJ9XX1d


commons.ai.sdk.intent.metadata=

commons.ai.sdk.vector.max.token.per_document=50000
commons.ai.sdk.vector.max.document.per_request=50
commons.ai.sdk.secret-key=secret-key
commons.ai.sdk.chat-size=10

exception.audit.enable=true
prompt.audit.enable=true
tool.audit.enable=true


spring.ai.openai.api-key=********************************************************
spring.ai.openai.chat.base-url=https://api.groq.com/openai/
spring.ai.openai.chat.options.model=llama-3.3-70b-versatile

##standalone,sentinel,standalone_ssl
#commons.ai.sdk.chat.redis.enable=true
#commons.ai.sdk.chat.redis.type=standalone_ssl

##standalone
#commons.ai.sdk.redis.host=localhost
#commons.ai.sdk.redis.port=6378

##standalone_ssl
#commons.ai.sdk.redis.host=localhost
#commons.ai.sdk.redis.port=6378
#commons.ai.sdk.redis.password=9fe9dad0-acb6-4e6b-8643-a350f35f502b
#commons.ai.sdk.chat.redis.trustStorePath=/Users/<USER>/Downloads/redis_gcp.p12
#commons.ai.sdk.chat.redis.trustStorePassword=changeit

##sentinel
#commons.ai.sdk.redis.sentinel.master=mymaster
#commons.ai.sdk.redis.sentinel.nodes=127.0.0.1:26379,127.0.0.1:26380,127.0.0.1:26381
#commons.ai.sdk.redis.password=
