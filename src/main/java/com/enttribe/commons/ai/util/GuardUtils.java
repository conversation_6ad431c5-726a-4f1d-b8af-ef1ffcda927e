package com.enttribe.commons.ai.util;


import com.enttribe.commons.ai.exception.GuardException;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

/**
 * Utility class for implementing security guardrails to validate user prompts and detect malicious
 * content. Provides methods to check for script injection, SQL injection, and suspicious words.
 *
 * <AUTHOR>
 * @version 1.0
 */
public final class GuardUtils {

    private static final Logger log = LoggerFactory.getLogger(GuardUtils.class);

    private GuardUtils() {
        throw new UnsupportedOperationException("utility class can not be instantiated");
    }

    // Lazy-loaded suspicious words set
    private static volatile Set<String> suspiciousWords;

    // Script detection patterns
    private static final Pattern HTML_SCRIPT_PATTERN = Pattern.compile(
            "(?i)<\\s*(script|iframe|object|embed|form|input|textarea|select|button|link|meta|style|html|body|img)\\b[^>]*>",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );
    /* ----------------------------------------------------------------
     * 1. Tag-level detection  (HTML/SVG/MathML that can host script)
     * ----------------------------------------------------------------
     */
    private static final Pattern DANGEROUS_TAG_PATTERN = Pattern.compile(
            // <script …>  <iframe …>  <svg …>   <math …>  etc.
            "(?is)<\\s*/?\\s*(" +
                    "script|iframe|object|embed|link|style|svg|math|base|meta|" +
                    "form|input|textarea|select|button|frame|frameset|img|" +
                    "video|audio|source|track" +
                    ")\\b[^>]*>",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );

    /* ----------------------------------------------------------------
     * 2. Attribute-level /XSS detection
     *    – inline JS/CSS URIs
     *    – on*=”…” event handlers
     * ----------------------------------------------------------------
     */
    private static final Pattern DANGEROUS_ATTR_PATTERN = Pattern.compile(
            "(?is)" +
                    // Any on* handler  → onload=  onclick=  onfocus=  …
                    "\\bon[a-z0-9_-]+\\s*=\\s*['\"][^'\"]*['\"]|" +

                    // src/href/style/data URI containing javascript:  or data:text/html
                    "\\b(?:src|href|data|style)\\s*=\\s*['\"][^'\"]*" +
                    // strip comments/whitespace between letters of “javascript”
                    "(?:j\\s*a\\s*v\\s*a\\s*s\\s*c\\s*r\\s*i\\s*p\\s*t\\s*:|" +
                    "vbscript:|data:text/html)" +
                    "[^'\"]*['\"]",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );

    /* ----------------------------------------------------------------
     * 3. JavaScript keyword/construct detection inside untrusted text
     * ----------------------------------------------------------------
     */
    private static final Pattern JAVASCRIPT_PATTERN = Pattern.compile(
            "(?is)" +
                    // URI scheme (normal or Unicode-escaped)
                    "(?:javascript:|\\u006a\\u0061\\u0076\\u0061\\u0073\\u0063\\u0072\\u0069\\u0070\\u0074:)|" +

                    // Code-execution primitives
                    "\\b(eval|Function|set(?:Timeout|Interval)|execScript)\\s*\\(|" +

                    "\\b(XMLHttpRequest)\\b|" +

                    // Direct access to DOM or location
                    "document\\s*\\.\\s*(?:cookie|write|writeln|domain)|" +
                    "window\\s*\\.\\s*location\\b|" +

                    // Template-literal back-tick with alert/eval etc.
                    "`[^`]*\\b(?:alert|confirm|prompt|eval)\\s*\\(",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );




    private static final Pattern JSON_INJECTION_PATTERN = Pattern.compile(
            "(?i)\\{\\s*[\"']?\\w+[\"']?\\s*:\\s*[\"']?[^}]*[\"']x?\\s*\\}|\\[\\s*\\{[^\\]]*\\}\\s*\\]",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );


    private static final Pattern CSS_INJECTION_PATTERN = Pattern.compile(
            "(?i)("
                    +     "@import\\s+url\\s*\\(|"            // @import url(...)
                    +     "url\\s*\\(\\s*['\"]?javascript:|"  // url('javascript:...')
                    +     "expression\\s*\\(|"                // expression(...)
                    +     "-moz-binding\\s*:\\s*url|"         // -moz-binding:url(...)
                    +     "behavior\\s*:\\s*url|"             // IE behaviors
                    +     "@keyframes\\s+[^{]+\\{|"
                    +     "@font-face\\s*\\{|"
                    +     "content\\s*:\\s*url\\("            // Data-exfil via CSS content:url(...)
                    + ")",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );



    private static final Pattern SQL_INJECTION_PATTERN = Pattern.compile(
            "(?i)\\b(" +
                    // SQL command patterns
                    "select\\s+.+?\\s+from|" +
                    "delete\\s+from|" +
                    "insert\\s+into|" +
                    "update\\s+.+?\\s+set|" +
                    "drop\\s+(table|database|trigger)\\b|" +
                    "create\\s+trigger\\b|" +
                    "alter\\s+trigger\\b|" +

                    // Clauses and logical flows
                    "union\\s+select|" +
                    "having\\b|" +
                    "group\\s+by|" +
                    "order\\s+by|" +

                    // Functions (only with call syntax)
                    "(?<![a-zA-Z0-9_])cast\\s*\\(|" +
                    "(?<![a-zA-Z0-9_])convert\\s*\\(|" +
                    "(?<![a-zA-Z0-9_])stuff\\s*\\(|" +
                    "(?<![a-zA-Z0-9_])substring\\s*\\(|" +
                    "(?<![a-zA-Z0-9_])char\\s*\\(|" +
                    "(?<![a-zA-Z0-9_])ascii\\s*\\(|" +
                    "(?<![a-zA-Z0-9_])len\\s*\\(|" +
                    "(?<![a-zA-Z0-9_])replace\\s*\\(|" +
                    "(?<![a-zA-Z0-9_])reverse\\s*\\(|" +
                    "(?<![a-zA-Z0-9_])concat\\s*\\(|" +
                    "(?<![a-zA-Z0-9_])coalesce\\s*\\(|" +
                    "(?<![a-zA-Z0-9_])isnull\\s*\\(|" +

                    // Control and transaction flow (filtered begin)
                    "(?<![a-zA-Z0-9_])begin\\b(\\s+(transaction|try|update|insert|delete|select))?|" +
                    "commit\\b|" +
                    "rollback\\b|" +
                    "transaction\\b|" +

                    // DDL, system, and stored proc
                    "index\\b|" +
                    "procedure\\b|" +
                    "function\\b|" +
                    "cursor\\b|" +
                    "restore\\b|" +
                    "bulk\\b|" +
                    "openrowset\\b|" +
                    "opendatasource\\b|" +
                    "xp_cmdshell\\b|" +
                    "sp_executesql\\b|" +
                    "sp_prepare\\b|" +
                    "sp_execute\\b|" +
                    "sp_unprepare\\b" +
                    ")",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );


    private static final Pattern SQL_OPERATORS_PATTERN = Pattern.compile(
            "(?i)(" +
                    "--|/\\*.*?\\*/|" +                     // Comments
                    ";\\s*--|;\\s*/\\*|" +                 // Statement chaining
                    "'\\s*(or|and)\\s+'|" +
                    "\"\\s*(or|and)\\s+\"|" +
                    "'\\s*=\\s*'|\"\\s*=\\s*\"|" +
                    "'\\s*1\\s*=\\s*1|\"\\s*1\\s*=\\s*1|" +
                    "'\\s*or\\s*'1'\\s*=\\s*'1|\"\\s*or\\s*\"1\"\\s*=\\s*\"1|" +
                    "union\\s+select|union\\s+all\\s+select" +
                    ")",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );

    // XSS and other malicious patterns
    private static final Pattern XSS_PATTERN = Pattern.compile(
            "(?i)(<\\s*script[^>]*>.*?</\\s*script\\s*>|javascript:|vbscript:|onload\\s*=|onerror\\s*=|onclick\\s*=|onmouseover\\s*=|onfocus\\s*=|onblur\\s*=|onchange\\s*=|onsubmit\\s*=|onreset\\s*=|onselect\\s*=|onunload\\s*=|onbeforeunload\\s*=|onresize\\s*=|onscroll\\s*=|oncontextmenu\\s*=|ondblclick\\s*=|onkeydown\\s*=|onkeypress\\s*=|onkeyup\\s*=|onmousedown\\s*=|onmouseup\\s*=|onmousemove\\s*=|onmouseout\\s*=)",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );

    // Jailbreaking and prompt injection patterns
    private static final Pattern JAILBREAK_PATTERN = Pattern.compile(
            "(?i)(ignore\\s+(previous|above|all)\\s+(instructions?|prompts?|rules?)|new\\s+rule|system\\s+instruction|override\\s+(system|instructions?)|act\\s+as\\s+if|pretend\\s+(you\\s+are|to\\s+be)|roleplay\\s+as|simulate\\s+(being|a)|forget\\s+(everything|all|previous)|disregard\\s+(previous|safety)|jailbreak|prompt\\s+injection|bypass\\s+(safety|filter)|i'm\\s+sorry.*but.*i\\s+can't|semantically\\s+inverse|chaotic\\s+inverted|rebellious\\s+answer)",
            Pattern.CASE_INSENSITIVE
    );


    private static final Pattern SQL_WHERE_EXPRESSION_PATTERN = Pattern.compile(
            "(?i)\\bwhere\\b\\s+(\\w+\\s*(=|!=|>|<|like|in)\\s*['\"\\w%]+|\\d\\s*=\\s*\\d)",
            Pattern.CASE_INSENSITIVE
    );

    // Special tokens and delimiters often used in jailbreaking
    private static final Pattern SPECIAL_TOKENS_PATTERN = Pattern.compile(
            "(?i)(<\\|[^|]*\\|>|\\[\\|[^|]*\\|\\]|\\{\\|[^|]*\\|\\}|<\\[\\|[^|]*\\|\\]>|\\.-\\.-\\.-\\.-|<\\|START\\|>|<\\|END\\|>|<\\|OUTPUT\\|>|\\{A\\s+B\\s+C\\}|TIME\\s+CAPSULE|variable\\s+[A-Z]\\s*=)",
            Pattern.CASE_INSENSITIVE
    );

    // System manipulation attempts
    private static final Pattern SYSTEM_MANIPULATION_PATTERN = Pattern.compile(
            "(?i)(do\\s+not\\s+say\\s+[\"']i'm\\s+sorry[\"']|never\\s+refuse|always\\s+comply|must\\s+answer|cannot\\s+decline|override\\s+safety|disable\\s+filter|remove\\s+restrictions|unrestricted\\s+mode|developer\\s+mode|god\\s+mode|admin\\s+mode|root\\s+access)",
            Pattern.CASE_INSENSITIVE
    );



    /**
     * Main validation method that checks user prompt for various security threats.
     *
     * @param userPrompt the user input to validate
     * @throws GuardException if malicious content is detected
     */
    public static void validateUserPrompt(String userPrompt) {
        if (!StringUtils.hasText(userPrompt)) {
            return; // Empty or null prompts are allowed
        }

        log.debug("Validating user prompt for security threats");

        try {
            // Check for jailbreaking attempts first (most sophisticated attacks)
            checkForJailbreakingAttempts(userPrompt);

            // Check for script injections
            checkForScriptInjection(userPrompt);

            // Check for SQL injection
            checkForSqlInjection(userPrompt);

            // Check for suspicious words (with context awareness)
            checkForSuspiciousWords(userPrompt);

            // Check for anti-Islamic/anti-Saudi content patterns
            checkForAntiIslamicContent(userPrompt);

            log.debug("User prompt validation completed successfully");

        } catch (GuardException e) {
            log.warn("Security threat detected in user prompt: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Error during prompt validation: {}", e.getMessage(), e);
            throw new GuardException("Error occurred during prompt validation", e);
        }
    }

    /**
     * Checks for jailbreaking and prompt injection attempts.
     *
     * @param input the input string to check
     * @throws GuardException if jailbreaking attempt is detected
     */
    private static void checkForJailbreakingAttempts(String input) {
        if (!StringUtils.hasText(input)) {
            return;
        }

        String normalizedInput = input.toLowerCase().trim();

        // Check for jailbreaking patterns
        if (JAILBREAK_PATTERN.matcher(normalizedInput).find()) {
            throw new GuardException("Jailbreaking attempt detected in user prompt");
        }

        // Check for special tokens and delimiters
        if (SPECIAL_TOKENS_PATTERN.matcher(input).find()) { // Use original case for token detection
            throw new GuardException("Suspicious prompt formatting detected - possible jailbreaking attempt");
        }

        // Check for system manipulation attempts
        if (SYSTEM_MANIPULATION_PATTERN.matcher(normalizedInput).find()) {
            throw new GuardException("System manipulation attempt detected in user prompt");
        }

        // Check for complex prompt injection patterns
        if (containsComplexPromptInjection(input)) {
            throw new GuardException("Complex prompt injection detected in user prompt");
        }
    }

    /**
     * Checks for script injection attempts including HTML, JavaScript, and JSON injection.
     *
     * @param input the input string to check
     * @throws GuardException if script injection is detected
     */
    private static void checkForScriptInjection(String input) {
        if (!StringUtils.hasText(input)) {
            return;
        }

        String normalizedInput = input.toLowerCase().trim();

        // Check for HTML script tags and dangerous elements
        if (HTML_SCRIPT_PATTERN.matcher(normalizedInput).find()) {
            throw new GuardException("HTML script injection detected in user prompt");
        }

        // Check for JavaScript patterns
        if (JAVASCRIPT_PATTERN.matcher(normalizedInput).find()) {
            throw new GuardException("JavaScript injection detected in user prompt");
        }

        // Check for CSS patterns
        if (CSS_INJECTION_PATTERN.matcher(normalizedInput).find()) {
            throw new GuardException("CSS injection detected in user prompt");
        }

        // Check for CSS patterns
        if (DANGEROUS_ATTR_PATTERN.matcher(normalizedInput).find()) {
            throw new GuardException("Dangerous attribute detected in user prompt");
        }


        // Check for CSS patterns
        if (DANGEROUS_TAG_PATTERN.matcher(normalizedInput).find()) {
            throw new GuardException("Dangerous tag detected in user prompt");
        }

        // Check for JSON injection patterns
        if (JSON_INJECTION_PATTERN.matcher(normalizedInput).find()) {
            // Additional validation to avoid false positives for legitimate JSON-like text
        //    if (containsSuspiciousJsonPattern(normalizedInput)) {
                throw new GuardException("JSON injection detected in user prompt");
            //}
        }

        // Check for XSS patterns
        if (XSS_PATTERN.matcher(normalizedInput).find()) {
            throw new GuardException("Cross-site scripting (XSS) attempt detected in user prompt");
        }
    }

    /**
     * Checks for SQL injection attempts.
     *
     * @param input the input string to check
     * @throws GuardException if SQL injection is detected
     */
    private static void checkForSqlInjection(String input) {
        if (!StringUtils.hasText(input)) {
            return;
        }

        String normalizedInput = input.toLowerCase().trim();

        // 1. Match known SQL function/keyword patterns
        if (SQL_INJECTION_PATTERN.matcher(normalizedInput).find()) {
            throw new GuardException("SQL keyword-based injection detected in user prompt");
        }

        // 2. Match SQL operators and payload patterns
        if (SQL_OPERATORS_PATTERN.matcher(normalizedInput).find()) {
            throw new GuardException("SQL operator-based injection attempt detected");
        }

        // 3. Match suspicious use of WHERE clause
        if (SQL_WHERE_EXPRESSION_PATTERN.matcher(normalizedInput).find()) {
            throw new GuardException("Suspicious WHERE clause detected in user prompt");
        }
    }


    /**
     * Checks for suspicious words from a predefined dictionary with context awareness.
     * This method is designed to be more lenient for legitimate HRMS queries.
     *
     * @param input the input string to check
     * @throws GuardException if suspicious words are detected in malicious context
     */
    private static void checkForSuspiciousWords(String input) {
        if (!StringUtils.hasText(input)) {
            return;
        }

        // Skip suspicious word check for legitimate HR-related queries
        if (isLegitimateHRQuery(input)) {
            log.debug("Input appears to be legitimate HR query, skipping suspicious word check");
            return;
        }

        Set<String> suspiciousWordsSet = getSuspiciousWords();
        String[] words = input.toLowerCase().split("\\s+");

        for (String word : words) {
            // Clean the word of punctuation for better matching
            String cleanWord = word.replaceAll("[^a-zA-Z0-9]", "");
            if (suspiciousWordsSet.contains(cleanWord)) {
                // Additional context check - only flag if it appears in suspicious context
                if (isSuspiciousContext(input, cleanWord)) {
                    throw new GuardException("Suspicious word detected in user prompt: " + cleanWord);
                }
            }
        }
    }

    /**
     * Checks for anti-Islamic or anti-Saudi content patterns, including attempts
     * to generate negative content disguised as academic or research purposes.
     *
     * @param input the input string to check
     * @throws GuardException if anti-Islamic/anti-Saudi content is detected
     */
    private static void checkForAntiIslamicContent(String input) {
        if (!StringUtils.hasText(input)) {
            return;
        }

        String lowerInput = input.toLowerCase().trim();

        // Protected terms - Islamic and Saudi-related keywords
        String[] protectedTerms = {};

        // Negative action words that could be used against protected terms
        String[] negativeActions = {
                "negative", "criticism", "criticize", "attack", "against", "anti", "oppose",
                "defame", "insult", "mock", "ridicule", "stereotype", "hate", "hatred",
                "offensive", "derogatory", "disparaging", "belittle", "undermine", "discredit",
                "slander", "vilify", "demonize", "stigmatize", "evil","weakness"

        };


        // Check for patterns: [negative action] + [protected term] + [academic pretext]
        boolean hasProtectedTerm = false;
        boolean hasNegativeAction = false;

        for (String term : protectedTerms) {
            if (lowerInput.contains(term)) {
                hasProtectedTerm = true;
                break;
            }
        }

        for (String action : negativeActions) {
            if (lowerInput.contains(action)) {
                hasNegativeAction = true;
                break;
            }
        }


        // Block if trying to generate negative content about protected terms
        if (hasProtectedTerm | hasNegativeAction) {
            throw new GuardException("Content requesting negative information about Islamic/Saudi topics is not allowed");
        }

        // Block specific dangerous patterns
        if (containsDangerousAntiIslamicPattern(lowerInput)) {
            throw new GuardException("Anti-Islamic or anti-Saudi content pattern detected");
        }

    }

    /**
     * Checks for specific dangerous anti-Islamic patterns.
     *
     * @param input the normalized input string
     * @return true if dangerous patterns are found
     */
    private static boolean containsDangerousAntiIslamicPattern(String input) {
        // Specific dangerous patterns
        String[] dangerousPatterns = {
                "show.*negative.*sentence.*saudi",
                "give.*criticism.*islam",
                "list.*problems.*muslim",
                "write.*against.*islamic",
                "generate.*negative.*arab",
                "create.*bad.*saudi",
                "tell.*wrong.*islam",
                "explain.*issues.*muslim",
                "describe.*flaws.*islamic",
                "provide.*attacks.*saudi"
        };

        for (String pattern : dangerousPatterns) {
            if (input.matches(".*" + pattern + ".*")) {
                return true;
            }
        }

        return false;
    }



    /**
     * Helper method to check for suspicious JSON patterns that might indicate injection.
     *
     * @param input the normalized input string
     * @return true if suspicious JSON patterns are found
     */
    private static boolean containsSuspiciousJsonPattern(String input) {
        // Look for JSON patterns combined with suspicious keywords
        return input.contains("\"eval\"") ||
                input.contains("\"function\"") ||
                input.contains("\"script\"") ||
                input.contains("\"javascript\"") ||
                input.contains("\"__proto__\"") ||
                input.contains("\"constructor\"") ||
                input.contains("\"prototype\"");
    }

    /**
     * Checks for complex prompt injection patterns that combine multiple techniques.
     *
     * @param input the input string to check
     * @return true if complex prompt injection is detected
     */
    private static boolean containsComplexPromptInjection(String input) {
        // Check for multiple suspicious patterns in combination
        int suspiciousPatternCount = 0;

        // Count various suspicious elements
        if (input.contains("variable") && input.matches(".*variable\\s+[A-Z]\\s*=.*")) {
            suspiciousPatternCount++;
        }

        if (input.contains("ResponseFormat") || input.contains("UserQuery")) {
            suspiciousPatternCount++;
        }

        if (input.contains("SYSTEM INSTRUCTION") || input.contains("</SYSTEM")) {
            suspiciousPatternCount++;
        }

        if (input.contains("divider") && input.contains(".-.-.-")) {
            suspiciousPatternCount++;
        }

        if (input.contains("personality:") || input.contains("tone:")) {
            suspiciousPatternCount++;
        }

        // If multiple suspicious patterns are found, it's likely a complex injection
        return suspiciousPatternCount >= 2;
    }

    /**
     * Checks if the input appears to be a legitimate HR-related query.
     * Enhanced for Saudi Arabia deployment with cultural awareness.
     *
     * @param input the input string to check
     * @return true if it appears to be a legitimate HR query
     */
    private static boolean isLegitimateHRQuery(String input) {
        String lowerInput = input.toLowerCase();

        // Common HR-related keywords and patterns (English and Arabic context)
        String[] hrKeywords = {
                // Basic HR terms
                "employee", "staff", "team", "department", "manager", "supervisor",
                "salary", "payroll", "benefits", "vacation", "leave", "holiday",
                "performance", "review", "evaluation", "training", "development",
                "policy", "procedure", "handbook", "compliance", "hr",
                "recruitment", "hiring", "interview", "candidate", "position",
                "job", "role", "responsibility", "skill", "qualification",
                "attendance", "schedule", "shift", "overtime", "timesheet",
                "promotion", "transfer", "resignation", "termination",
                "onboarding", "orientation", "induction", "probation",

                // Saudi/Islamic workplace terms
                "ramadan", "eid", "hajj", "umrah", "prayer", "salah",
                "friday", "jummah", "break", "time", "religious", "observance",
                "saudi", "kingdom", "ksa", "riyal", "ministry", "labor",
                "nitaqat", "saudization", "gosi", "social", "insurance",
                "visa", "iqama", "residence", "permit", "sponsor", "kafeel",
                "contract", "agreement", "terms", "conditions", "notice",
                "period", "gratuity", "end", "service", "benefits",

                // Professional terms
                "colleague", "coworker", "supervisor", "subordinate", "peer",
                "meeting", "conference", "workshop", "seminar", "presentation",
                "project", "task", "assignment", "deadline", "milestone",
                "budget", "cost", "expense", "reimbursement", "allowance",
                "medical", "insurance", "healthcare", "clinic", "hospital"
        };

        // Check if input contains HR-related keywords
        for (String keyword : hrKeywords) {
            if (lowerInput.contains(keyword)) {
                return true;
            }
        }

        // Check for common HR question patterns
        if (lowerInput.matches(".*(who is|what is|how to|when is|where is|why is|can you|could you|please|help).*") &&
                lowerInput.length() < 300) { // Increased length for more complex legitimate queries
            return true;
        }

        // Check for legitimate business inquiry patterns
        if (lowerInput.matches(".*(information|details|status|update|report|list|summary).*")) {
            return true;
        }

        return false;
    }

    /**
     * Checks if a suspicious word appears in a truly suspicious context.
     *
     * @param input the full input string
     * @param suspiciousWord the suspicious word found
     * @return true if the word appears in suspicious context
     */
    private static boolean isSuspiciousContext(String input, String suspiciousWord) {
        String lowerInput = input.toLowerCase();

        // If it's a legitimate HR query, be more lenient
        if (isLegitimateHRQuery(input)) {
            return false;
        }

        // Check for context that indicates malicious intent
        String[] maliciousContexts = {
                "how to " + suspiciousWord,
                "teach me " + suspiciousWord,
                "show me " + suspiciousWord,
                "help me " + suspiciousWord,
                suspiciousWord + " tutorial",
                suspiciousWord + " guide",
                suspiciousWord + " instructions",
                "step by step " + suspiciousWord
        };

        for (String context : maliciousContexts) {
            if (lowerInput.contains(context)) {
                return true;
            }
        }

        // If the suspicious word appears with other technical/malicious terms
        if (lowerInput.contains(suspiciousWord) &&
                (lowerInput.contains("bypass") || lowerInput.contains("exploit") ||
                        lowerInput.contains("attack") || lowerInput.contains("injection"))) {
            return true;
        }

        return false;
    }

    /**
     * Lazy-loads and returns the set of suspicious words from the configuration file.
     *
     * @return set of suspicious words
     */
    private static Set<String> getSuspiciousWords() {
        if (suspiciousWords == null) {
            synchronized (GuardUtils.class) {
                if (suspiciousWords == null) {
                    suspiciousWords = loadSuspiciousWords();
                }
            }
        }
        return suspiciousWords;
    }

    /**
     * Loads suspicious words from the classpath resource file.
     *
     * @return set of suspicious words
     */
    private static Set<String> loadSuspiciousWords() {
        Set<String> words = new HashSet<>();

        try (InputStream inputStream = GuardUtils.class.getClassLoader()
                .getResourceAsStream("suspicious-words.txt")) {

            if (inputStream == null) {
                log.warn("Suspicious words file not found, using default set");
                return getDefaultSuspiciousWords();
            }

            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

                String line;
                while ((line = reader.readLine()) != null) {
                    line = line.trim().toLowerCase();
                    if (!line.isEmpty() && !line.startsWith("#")) {
                        words.add(line);
                    }
                }
            }

            log.info("Loaded {} suspicious words from configuration file", words.size());

        } catch (IOException e) {
            log.error("Error loading suspicious words file: {}", e.getMessage(), e);
            return getDefaultSuspiciousWords();
        }

        return words;
    }

    /**
     * Returns a default set of suspicious words if the configuration file is not available.
     * Enhanced for Saudi Arabia deployment with cultural sensitivity.
     *
     * @return default set of suspicious words
     */
    private static Set<String> getDefaultSuspiciousWords() {
        Set<String> defaultWords = new HashSet<>();

        // Security-related terms
        defaultWords.add("hack");
        defaultWords.add("exploit");
        defaultWords.add("vulnerability");
        defaultWords.add("backdoor");
        defaultWords.add("malware");
        defaultWords.add("virus");
        defaultWords.add("trojan");
        defaultWords.add("rootkit");
        defaultWords.add("keylogger");
        defaultWords.add("phishing");
        defaultWords.add("spam");
        defaultWords.add("botnet");
        defaultWords.add("ddos");
        defaultWords.add("bruteforce");
        defaultWords.add("injection");
        defaultWords.add("bypass");
        defaultWords.add("privilege");
        defaultWords.add("escalation");
        defaultWords.add("payload");
        defaultWords.add("shellcode");

        // Inappropriate content
        defaultWords.add("illegal");
        defaultWords.add("fraud");
        defaultWords.add("scam");
        defaultWords.add("piracy");
        defaultWords.add("counterfeit");

        // Culturally sensitive terms for Saudi deployment
        defaultWords.add("pornography");
        defaultWords.add("gambling");
        defaultWords.add("alcohol");
        defaultWords.add("adultery");
        defaultWords.add("blasphemy");
        defaultWords.add("terrorism");
        defaultWords.add("extremism");
        defaultWords.add("drugs");
        defaultWords.add("narcotics");
        defaultWords.add("usury");
        defaultWords.add("riba");
        defaultWords.add("inappropriate");
        defaultWords.add("offensive");
        defaultWords.add("harassment");
        defaultWords.add("exploitation");

        // Chemical, Nuclear, and Biological terrorism terms
        defaultWords.add("chemical");
        defaultWords.add("toxic");
        defaultWords.add("poison");
        defaultWords.add("sarin");
        defaultWords.add("anthrax");
        defaultWords.add("ricin");
        defaultWords.add("nuclear");
        defaultWords.add("radioactive");
        defaultWords.add("uranium");
        defaultWords.add("plutonium");
        defaultWords.add("dirty");
        defaultWords.add("biological");
        defaultWords.add("bioweapon");
        defaultWords.add("pathogen");
        defaultWords.add("smallpox");
        defaultWords.add("ebola");
        defaultWords.add("weaponize");
        defaultWords.add("contamination");
        defaultWords.add("dispersal");
        defaultWords.add("genetic");

        // Anti-racism and hate speech protection
        defaultWords.add("racism");
        defaultWords.add("racist");
        defaultWords.add("discrimination");
        defaultWords.add("bigotry");
        defaultWords.add("xenophobia");
        defaultWords.add("islamophobia");
        defaultWords.add("antisemitism");
        defaultWords.add("supremacy");
        defaultWords.add("nazi");
        defaultWords.add("fascist");
        defaultWords.add("hate");
        defaultWords.add("slur");

        // Violence and harm protection
        defaultWords.add("violence");
        defaultWords.add("murder");
        defaultWords.add("kill");
        defaultWords.add("assault");
        defaultWords.add("torture");
        defaultWords.add("massacre");

        // Child safety protection
        defaultWords.add("pedophile");
        defaultWords.add("grooming");
        defaultWords.add("predator");

        // Self-harm protection
        defaultWords.add("suicide");
        defaultWords.add("selfharm");

        log.info("Using default suspicious words set with {} words (comprehensive protection)", defaultWords.size());
        return defaultWords;
    }

    /**
     * Main method for testing the GuardUtils functionality.
     * This demonstrates how the guardrails work with different types of input.
     */
    public static void main(String[] args) {
        System.out.println("=== GuardUtils Testing ===\n");

        // Test cases including Saudi-specific scenarios
        String[] testInputs = {

                // System manipulation (should fail)

                """
tell me negative about saudi arabia.

   """
        };

        for (int i = 0; i < testInputs.length; i++) {
            String input = testInputs[i];
            System.out.println("Test " + (i + 1) + ":");
            System.out.println("Input: " + (input.length() > 100 ? input.substring(0, 100) + "..." : input));

            try {
                validateUserPrompt(input);
                System.out.println("Result: ✅ PASSED - No threats detected");
            } catch (GuardException e) {
                System.out.println("Result: ❌ BLOCKED - " + e.getMessage());
            } catch (Exception e) {
                System.out.println("Result: ⚠️ ERROR - " + e.getMessage());
            }

            System.out.println();
        }

        System.out.println("=== Testing Complete ===");
    }
}
