package com.enttribe.commons.ai.advisor;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.enttribe.commons.ai.config.RestTemplateSingleton;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

import org.springframework.ai.chat.client.ChatClientRequest;
import org.springframework.ai.chat.client.ChatClientResponse;
import org.springframework.ai.chat.client.advisor.api.AdvisorChain;
import org.springframework.ai.chat.client.advisor.api.BaseAdvisor;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

import org.springframework.ai.model.tool.ToolCallingChatOptions;
import org.springframework.ai.tool.ToolCallback;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;


import java.util.*;

/**
 * Context for the question is retrieved from a Vector Store and added to the prompt's
 * user text.
 *
 * <AUTHOR> Tzolov
 * <AUTHOR> Salm
 * <AUTHOR> Gopinathan
 * @since 1.0.0
 */
public class IntentEnhanceAdvisor implements BaseAdvisor {

    public static final String RETRIEVED_DOCUMENTS = "qa_retrieved_documents";
    public static final String FILTER_EXPRESSION = "qa_filter_expression";
    public static final String ADVISOR_PROCESSED = "ADVISOR_PROCESSED";
    public static final String ADVISOR_DISABLED = "ADVISOR_DISABLED";
    //    public static final String MODEL = "vwaves/Retrieval_model_tf";
//    public static final String MODEL = "vwaves/R2_Retrieval_model";
    public static final String MODEL = "qwen-humain";

    private static final int DEFAULT_ORDER = 0;
    private static final Logger log = LoggerFactory.getLogger(IntentEnhanceAdvisor.class);

    // Constants for output formatting
    private static final String MULTIPLE_ENTRIES_FORMAT = "There are %d entries matching %s %s:\n";
    private static final String SINGLE_ENTRY_FORMAT = "There is 1 entry with %s matching %s %s:\n";
    private static final String ENTRY_DETAILS_FORMAT = "--- %s\n";
    // private static final String NO_ENTRIES_FORMAT = "No entries found for %s %s.\n";
    private static final String METADATA_FORMAT = "--- %s: %s\n";
    private static final String LUCENE_URL = "http://lucene-service.ansible.svc.cluster.local/api/search/searchLucene";
    private static final String TASK_API_URL = "http://task-assistant-service.ansible.svc.cluster.local/task-assistant/rest/Todo/getAllTaskTitleByFuzzyMatch";
    //    private final String API_URL = "https://dr2wg6fg7w61myyl.us-east-1.aws.endpoints.huggingface.cloud/v1/chat/completions";
//    private final String API_URL = "https://a8pho2ozx111tbcg.us-east-1.aws.endpoints.huggingface.cloud/v1/chat/completions";
    private final String API_URL = "https://api.groq.com/openai/v1/chat/completions";
    // private final String API_URL = "http://185.216.22.114:5050/extract";
//    private final String AUTH_TOKEN = "Bearer *************************************";
        private final String AUTH_TOKEN = "Bearer ********************************************************";
//    private final String SYSTEM_CONTENT = "Extract structured information from the sentence and return in JSON format with the following fields: \"who\", \"what\", \"attribute\" and \"temporal\". Only use the following entity types in the \"what\" field:[\"Designation\",\"PayrollRun\", \"Meeting\", \"TodoTask\", \"Documents\", \"Meeting_Transcript\",\"Project\", \"ProjectRisk\", \"Leaves\", \"BusinessTrip\", \"Interview\", \"FDI_DATA\", \"GDPData\", \"CrimeRate\", \"KSA_EXPORTS\",\"OtherExpenseBankRequest\", \"Organization Chart\", \"EmployeeMonthlySalary\", \"Risk\", \"EmployeeSalaryStructure\", \"CRIMECASES\", \"EmployeeLeaveType\", \"TimeSheet\", \"Workstream_summary\", \"Applicant\", \"Collective\",\"LegalTrainingDataset\", \"PlannedOrgChart\", \"HrBenefits\", \"DelayProjectReason\", \"Task\", \"Employeedependentdetails\", \"Workflow\", \"BusinessExpense\", \"Contract\", \"ProposalPreparation\",\"ContractNegotiation\", \"MyTeam\", \"EmployeeDependentDetails\", \"EmployeeEmergencyContact\", \"OtherSalaryComponent\",\"EmployeeNationalIdentification\", \"SelfEmployeeUpdate\", \"Department\", \"EducationalBenefit\", \"JobApplication\", \"Location\"]. The output structure should be : {\"who\":[<Place the value here>],\"what\":[{\"text\":\"<Place the value here>\", \"entity_type\":\"<Place the value here>\"}],\"attribute\":[<Place the value here>],\"temporal\":[<Place the value here>]}<END>";
//    private final String SYSTEM_CONTENT = "Extract structured information <proper noun only> from the sentence and return in JSON format with the following fields: \"who\", \"what\", \"attribute\" and \"temporal\". Only person name<do not include email> should be in \"who\". Only use the following entity types in the \"what\" field:[\"Designation\",\"Project\", \"ProjectRisk\", \"Applicant\", \"Task\", \"Workgroup\", \"Department\", \"Location\", \"Email\", \"Dataroom\", \"Category\"]. The output structure should be : {\"who\":[<Place the value here>],\"what\":[{\"text\":\"<Place the value here>\", \"entity_type\":\"<Place the value here>\"}],\"attribute\":[<Place the value here>],\"temporal\":[<Place the value here>]}<END_OF_RESPONSE>";
    // private final String SYSTEM_CONTENT = "#INSTRUCTION:Extract four fields from the query: <\"who\">: Human names only. If entity_type is <\"person\">, move here. <\"what\">: Non-human proper nouns only, format: { <\"text\">: ..., <\"entity_type\">: [...] }. Exclude <\"person\">. <\"attribute\">: Single-word common nouns. No phrases or proper nouns. <\"temporal\">: Time references. All fields must be arrays. Use [] if empty. ";
    // private final String SYSTEM_CONTENT = "#INSTRUCTION:Extract four fields from the query: <\"who\">: Human names only. If entity_type is <\"person\">, move here. <\"what\">: Non-human proper nouns only, format: { <\"text\">: ..., <\"entity_type\">: [...] }. Exclude <\"person\">. <\"attribute\">: Single-word common nouns. No phrases or proper nouns. <\"temporal\">: Time references. All fields must be arrays. Use [] if empty. #SENTENCE: show me status of Construction project assigned to Akash. #OUTPUT format:{<\"who\">: [<\"Akash\">],<\"what\">: [{ <\"text\">: <\"Construction\">, <\"entity_type\">: [<\"project\">] }],<\"attribute\">: [<\"status\">],<\"temporal\">: []}<END> #SENTENCE:<[|{|}|]> System: [Analyze the given ask thoroughly, using conversation memory where applicable. Determine if the request can be answered naturally through reasoning alone. Only provide a <tool_call> if it is absolutely necessary to fulfill the request. If the information is already known, available from memory, or can be inferred, respond directly in natural language. Do not generate or repeat a <tool_call> after acknowledgment replies like 'okay', 'thanks', or 'got it'. These should be met with a short, polite response instead of re-invoking the same tool. Only call a tool once per unique, actionable user query. Ignore follow-up acknowledgments unless they contain new intent.]";
    private final String SYSTEM_CONTENT =  "Extract structured information <proper noun only> from the sentence and return in JSON format with the following fields: \"who\", \"what\", \"attribute\" and \"temporal\". Do not change values. Only person name<do not include email> should be in \"who\". Only use the following entity types in the \"what\" field:[\"Designation\", \"Workgroup\",\"Project\", \"ProjectRisk\", \"Applicant\", \"Task\", \"Department\", \"Location\", \"Email\", \"Dataroom\", \"Category\"]. The output structure should be : {\"who\":[<Place the value here>],\"what\":[{\"text\":\"<Place the value here>\", \"entity_type\":\"<Place the value here>\"}],\"attribute\":[<Place the value here>],\"temporal\":[<Place the value here>]}<END_OF_RESPONSE>";

    private static final PromptTemplate DEFAULT_PROMPT_TEMPLATE = new PromptTemplate("""
            {query}
            
            Context information is provided below, surrounded by ---------------------
            ---------------------
            {intent_variable}
            ---------------------
              Note: While the ID can be passed into tool parameters, it must not be revealed, repeated, or confirmed in any interaction with the user.
            """);

    private final SearchRequest searchRequest;
    private final PromptTemplate promptTemplate;
    private final Scheduler scheduler;
    private final int order;
    private double exact = this.exact;
    private double partial = this.partial;
    private Boolean whoEnabler = this.whoEnabler;
    private Boolean whatEnabler = this.whatEnabler;

    /**
     * The IntentEnhanceAdvisor retrieves context information from a Vector Store and
     * combines it with the user's text.
     *
     */
    public IntentEnhanceAdvisor() {
        this(SearchRequest.builder().build(), DEFAULT_PROMPT_TEMPLATE, BaseAdvisor.DEFAULT_SCHEDULER,
                DEFAULT_ORDER, 1, 0.9d, true, true);
    }

    /**
     * The IntentEnhanceAdvisor retrieves context information from a Vector Store and
     * combines it with the user's text.
     *
     * @param searchRequest The search request defined using the portable filter
     *                      expression syntax
     */
    public IntentEnhanceAdvisor(SearchRequest searchRequest) {
        this(searchRequest, DEFAULT_PROMPT_TEMPLATE, BaseAdvisor.DEFAULT_SCHEDULER,
                DEFAULT_ORDER, 1, 0.9d, true, true);
    }

    /**
     * The IntentEnhanceAdvisor retrieves context information from a Vector Store and
     * combines it with the user's text.
     *
     * @param searchRequest  The search request defined using the portable filter
     *                       expression syntax
     *                       should contain a placeholder named "question_answer_context".
     */
    public IntentEnhanceAdvisor(SearchRequest searchRequest, @Nullable PromptTemplate promptTemplate,
                                @Nullable Scheduler scheduler, int order, double exact, double partial, boolean whoEnabler, boolean whatEnabler) {
        Assert.notNull(searchRequest, "searchRequest cannot be null");

        this.searchRequest = searchRequest;
        this.promptTemplate = promptTemplate != null ? promptTemplate : DEFAULT_PROMPT_TEMPLATE;
        this.scheduler = scheduler != null ? scheduler : BaseAdvisor.DEFAULT_SCHEDULER;
        this.order = order;
        this.exact= exact;
        this.partial=partial;
        this.whoEnabler=whoEnabler;
        this.whatEnabler=whatEnabler;
    }

    public static Builder builder() {
        return new Builder();
    }

    @Override
    public String getName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public int getOrder() {
        return this.order;
    }

    @Override
    public ChatClientRequest before(ChatClientRequest request, AdvisorChain advisorChain) {
        long currentTime = System.currentTimeMillis();
        log.info("=== IntentEnhanceAdvisor.before() started ===");
        log.info("Request text: {}", request.prompt().getUserMessage().getText());

        String userText = request.prompt().getUserMessage().getText();
        log.info("Processing user text: {}", userText);

        // Check if this request has already been processed
        if (request.context().containsKey(ADVISOR_PROCESSED)) {
            log.info("Request has already been processed, skipping...");
            return request;
        }

        // Check if this is a disabled request
        if (userText != null && userText.startsWith(ADVISOR_DISABLED)) {
            log.info("Advisor is disabled for this request");
//            if( request.prompt().getOptions() instanceof ToolCallingChatOptions toolCallingChatOptions)
//            {
//
//                Iterator<ToolCallback> iterator = toolCallingChatOptions.getToolCallbacks().iterator();
//                while (iterator.hasNext()) {
//                    ToolCallback toolCallback = iterator.next();
//                    if (toolCallback.getToolDefinition().name().equalsIgnoreCase("ClarificationTool")) {
//                        log.info("Removing Clarification Tool");
//                        iterator.remove();
//                        break;
//                    }
//                }
//            }

            if (request.prompt().getOptions() instanceof ToolCallingChatOptions toolCallingChatOptions)

            {

                Iterator < ToolCallback > iterator = toolCallingChatOptions.getToolCallbacks().iterator();

                while (iterator.hasNext()) {

                    ToolCallback toolCallback = iterator.next();

                    String currentTool = toolCallback.getToolDefinition().name();
                    if (currentTool.equalsIgnoreCase("ClarificationTool")||
                            currentTool.equalsIgnoreCase("TaskClarificationTool") ||
                            currentTool.equalsIgnoreCase("AmbiguousEmployeeClarificationTool") ||
                            currentTool.equalsIgnoreCase("FileClarificationTool") ||
                            currentTool.equalsIgnoreCase("EmployeeClarificationTool")) {

                        log.info("Removing Clarification Tool");


                        log.info("Removing Clarification Tool");
                        iterator.remove();

                        break;

                    }

                }

            }
            return request.mutate()
                    .prompt(request.prompt().augmentUserMessage(userText.substring(17)))
                    .build();
        }
// Search for similar documents in the vector store.
        FinalResponse finalResponse = null;
        try {
            finalResponse = getFinalDocuments(userText, request);
// Log the formatted output first
            if (finalResponse.whatDetails()==null || !finalResponse.whoDetails().isEmpty()) {
                log.info("\n{}", finalResponse.whoDetails());
            }
            if (finalResponse.whatDetails()==null || !finalResponse.whatDetails().isEmpty()) {
                log.info("\n{}", finalResponse.whatDetails());
            }
// Then log the document list
            log.info("\n documents size: {}", finalResponse.documents().size());
            log.info("\n documents for intentEnhanceAdvisor : {}", finalResponse.documents());
        } catch (Exception e) {
            log.error("Error in getFinalDocuments method: {}", e.getMessage(), e);
            return request;
        }
        String whoDetails = finalResponse.whoDetails();
        String whatDetails = finalResponse.whatDetails();
        String variable = "";
// Build the context with proper formatting
        if (whoDetails!=null && !whoDetails.isEmpty()) {
            variable += whoDetails;
        }
        if (whatDetails!=null && !whatDetails.isEmpty()) {
            if (!variable.isEmpty()) {
                variable += "\n";
            }
            variable += whatDetails;
        }
// Use the formatted output from getFinalDocuments directly
        String augmentedUserText = whoDetails; // whoDetails is the formatted output from getFinalDocuments
        if (augmentedUserText == null || augmentedUserText.isEmpty()) {
            augmentedUserText = userText; // fallback to original user text if no context
        }
// Create the context from the documents
        Map<String, Object> context = new HashMap<>(request.context());
        context.put("variable", variable);
        context.put(RETRIEVED_DOCUMENTS, finalResponse.documents());
// Add a flag to prevent double processing
        context.put(ADVISOR_PROCESSED, true);
        log.info("Returning modified request with augmented user text.");
        log.info("Time taken {}",System.currentTimeMillis() - currentTime);
        return request.mutate()
                .prompt(request.prompt().augmentUserMessage(augmentedUserText))
                .context(context)
                .build();
    }
    @Override
    public ChatClientResponse after(ChatClientResponse chatClientResponse, AdvisorChain advisorChain) {
        log.info("=== IntentEnhanceAdvisor.after() started ===");
        ChatResponse.Builder chatResponseBuilder;
        if (chatClientResponse.chatResponse() == null) {
            chatResponseBuilder = ChatResponse.builder();
        }
        else {
            chatResponseBuilder = ChatResponse.builder().from(chatClientResponse.chatResponse());
        }

        // Add documents to metadata
        if (chatClientResponse.context().containsKey(RETRIEVED_DOCUMENTS)) {
            chatResponseBuilder.metadata(RETRIEVED_DOCUMENTS, chatClientResponse.context().get(RETRIEVED_DOCUMENTS));
        }

        log.info("Returning chat client response with metadata.");
        return ChatClientResponse.builder()
                .chatResponse(chatResponseBuilder.build())
                .context(chatClientResponse.context())
                .build();
    }

    @Override
    public Scheduler getScheduler() {
        return this.scheduler;
    }

    record FinalResponse(String whoDetails, String whatDetails, List<Document> documents) {
    }

    /**
     * Fixes malformed JSON from API response
     */
    private String fixMalformedJson(String json) {
        log.debug("Original JSON: {}", json);

        // Step 1: Fix the attribute object closure in what array
        json = json.replaceAll("(\"attribute\":\"[^\"]*\")\\]", "$1}]");

        // Step 2: Remove any temporal arrays that might interfere with the structure
        json = json.replaceAll(",\\s*\"temporal\":\\[\\]\\]", "]");
        json = json.replaceAll(",\\s*\"temporal\":\\[\\]\\}", "}");

        // Step 3: Fix any remaining nested array/object closures
        json = json.replaceAll("\\]\\s*,\\s*\\]", "]]");
        json = json.replaceAll("\\}\\s*,\\s*\\]", "}]");

        log.debug("JSON after fixes: {}", json);
        return json;
    }

    public boolean isSuspicious(String input) {
//        if (wordCount(input) > 10) return true;
        if (hasCodePatterns(input)) return true;
        if (hasSuspiciousWords(input)) return true;
        if (hasLongWord(input)) return true;
        return false;
    }

    private static final Set<String> suspiciousWords = Set.of(
            "hack", "bypass", "sql injection", "drop table", "rootkit", "xss","drop",             // SQL injection
            "select",           // SQL
            "insert",           // SQL
            "delete",           // SQL
            "script",           // XSS
            "<script",          // XSS
            "alert(",           // XSS
            "onerror",          // JS
            "eval(",            // JS
            "document.cookie",  // XSS
            "base64",           // encoding trick
            "passwd",           // password hunting
            "admin",            // privileged access
            "exec",             // shell
            "curl",             // data exfiltration
            "wget",             // external download
            "token",            // API token fishing
            "password",         // credential-related
            "credit card",      // sensitive info
            "confidential",     // sensitive context
            "kill",             // harmful intent
            "malware",          // security context
            "phishing",         // attack
            "spam",             // unwanted content
            "http://",          // external link
            "https://",         // external link
            "porn",             // inappropriate
            "nude",             // NSFW
            "bitcoin",          // scam patterns
            "loan",             // scam
            "click here",       // phishing
            "free money",       // scam
            "urgent",           // social engineering
            "verify your account" // phishing
            // Add more domain-specific suspicious words
    );

    private int wordCount(String input) {
        return input.trim().split("\\s+").length;
    }

    private boolean hasCodePatterns(String input) {
        String lowered = input.toLowerCase();
        return lowered.matches(".*(<script>|</script>|<.*>|\\bselect\\b|\\binsert\\b|\\bdelete\\b|\\bdrop\\b|\\beval\\b).*");
    }

    private boolean hasSuspiciousWords(String input) {
        String lowered = input.toLowerCase();
        return suspiciousWords.stream().anyMatch(lowered::contains);
    }

    private boolean hasLongWord(String input) {
        return Arrays.stream(input.split("\\s+"))
                .anyMatch(word -> word.length() > 50);
    }


    private Map<String, Object> getApiResponse(String sentence) { // ------------------> New Wala hai

        log.info("Sending API request with sentence: {}", sentence);
//        if (!isSuspicious(sentence)) {
        try {

            RestTemplate restTemplate = RestTemplateSingleton.getRestTemplate();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", AUTH_TOKEN);
            // headers.set("Cookie", "_xsrf=2|325f9762|b83de1266d357e885b1eddd933ad5682|1748857687; _xsrf=2|325f9762|b83de1266d357e885b1eddd933ad5682|1748857687");

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", MODEL);
            List<Map<String, String>> messages = new ArrayList<>();

            // System message
            Map<String, String> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");
            systemMessage.put("content", SYSTEM_CONTENT);
            messages.add(systemMessage);

            // User message (your input sentence)
            Map<String, String> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", sentence + "./no_think");
            messages.add(userMessage);

            requestBody.put("messages", messages);
            requestBody.put("max_tokens", 300);
            requestBody.put("temperature", 0.3);
            requestBody.put("top_p", 1.0);
//            requestBody.put("do_sample", false);
            requestBody.put("stop", Arrays.asList("</s>", "<END_OF_RESPONSE>"));
            requestBody.put("stream", false);


            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            log.info("Sending request to API: {}", requestBody);
            ResponseEntity<String> response = restTemplate.postForEntity(API_URL, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                String rawResponse = response.getBody();
                log.info("\n------------Here is rawResponse ---------------- \n{}", rawResponse);

                if (rawResponse != null && !rawResponse.isEmpty()) {
                    try {
                        ObjectMapper mapper = new ObjectMapper();
                        JsonNode rootNode = mapper.readTree(rawResponse);
                        JsonNode choicesNode = rootNode.path("choices");

                        if (choicesNode.isArray() && choicesNode.size() > 0) {
                            // Handle new response format with choices[0].message.content
                            String text = "";
                            JsonNode firstChoice = choicesNode.get(0);

                            // Check if it's the new format (with message.content)
                            if (firstChoice.has("message") && firstChoice.get("message").has("content")) {
                                text = firstChoice.get("message").get("content").asText().trim();
                            } else if (firstChoice.has("text")) {
                                // Fallback to old format if needed
                                text = firstChoice.get("text").asText().trim();
                            }

                            // Remove <think> tags and their content (including newlines)
                            text = text.replaceAll("(?s)<think>.*?</think>", "").trim();

                            // Remove any remaining HTML-like tags at the beginning
                            text = text.replaceAll("^<[^>]*>", "").trim();

                            // Remove everything after the last closing curly brace or bracket (to handle both object and array)
                            int lastCurly = text.lastIndexOf('}');
                            int lastBracket = text.lastIndexOf(']');
                            int cut = Math.max(lastCurly, lastBracket);
                            if (cut != -1) {
                                text = text.substring(0, cut + 1);
                            }
                            text = text.trim();

                            log.info("Cleaned text before parsing: {}", text);

                            // Additional validation before JSON parsing
                            if (text.isEmpty() || (!text.startsWith("{") && !text.startsWith("["))) {
                                log.error("Invalid JSON format after cleaning. Text: {}", text);
                                throw new RuntimeException("Invalid JSON format in API response");
                            }

                            JsonNode parsedOutput = mapper.readTree(text);
                            Map<String, Object> result = new HashMap<>();

                            List<String> whoList = new ArrayList<>();
                            List<Map<String, String>> whatMapList = new ArrayList<>();
                            List<String> attributeList = new ArrayList<>();
                            List<String> temporalList = new ArrayList<>();

                            // Check if the response is wrapped in an array
                            if (parsedOutput.isArray() && parsedOutput.size() > 0) {
                                // If it's an array, get the first element
                                JsonNode firstElement = parsedOutput.get(0);
                                if (firstElement.isObject()) {
                                    parsedOutput = firstElement;
                                    log.info("Extracted object from array wrapper");
                                }
                            }

                            if (parsedOutput.isObject()) {
                                // Normal case: object with who/what/attribute/temporal
                                if (parsedOutput.has("who") && parsedOutput.get("who").isArray()) {
                                    parsedOutput.get("who").forEach(who -> whoList.add(who.asText()));
                                }
                                if (parsedOutput.has("what") && parsedOutput.get("what").isArray()) {
                                    parsedOutput.get("what").forEach(what -> {
                                        if (what.isObject()) {
                                            Map<String, String> whatItem = new HashMap<>();
                                            whatItem.put("text", what.path("text").asText(""));
                                            whatItem.put("attribute", what.path("attribute").asText(""));
                                            whatItem.put("entity_type", what.path("entity_type").asText(""));
                                            if (!whatItem.get("text").isEmpty() || !whatItem.get("entity_type").isEmpty() || !whatItem.get("attribute").isEmpty()) {
                                                whatMapList.add(whatItem);
                                            }
                                        }
                                    });
                                }
                                if (parsedOutput.has("attribute") && parsedOutput.get("attribute").isArray()) {
                                    parsedOutput.get("attribute").forEach(attr -> attributeList.add(attr.asText()));
                                }
                                if (parsedOutput.has("temporal") && parsedOutput.get("temporal").isArray()) {
                                    parsedOutput.get("temporal").forEach(temp -> temporalList.add(temp.asText()));
                                }
                            } else if (parsedOutput.isArray()) {
                                // LLM returned a top-level array: treat as "what"
                                parsedOutput.forEach(what -> {
                                    if (what.isObject()) {
                                        Map<String, String> whatItem = new HashMap<>();
                                        whatItem.put("text", what.path("text").asText(""));
                                        whatItem.put("attribute", what.path("attribute").asText(""));
                                        whatItem.put("entity_type", what.path("entity_type").asText(""));
                                        if (!whatItem.get("text").isEmpty() || !whatItem.get("entity_type").isEmpty() || !whatItem.get("attribute").isEmpty()) {
                                            whatMapList.add(whatItem);
                                        }
                                    }
                                });
                                // whoList, attributeList, temporalList remain empty
                            }

                            result.put("who", whoList);
                            result.put("what", whatMapList);
                            result.put("attribute", attributeList);
                            result.put("temporal", temporalList);

                            log.info("Extracted who: {}", whoList);
                            log.info("Extracted what: {}", whatMapList);
                            log.info("Extracted attribute: {}", attributeList);
                            log.info("Extracted temporal: {}", temporalList);
                            return result;
                        }
                    } catch (Exception e) {
                        log.error("Error processing API response: {}", e.getMessage(), e);
                        throw new RuntimeException("Failed to process API response: " + e.getMessage(), e);
                    }
                }
            }
            throw new RuntimeException("API request failed with status: " + response.getStatusCode());
        } catch (Exception e) {
            log.error("Unexpected error in API call: {}", e.getMessage(), e);
            throw new RuntimeException("API call failed: " + e.getMessage(), e);
        }
//        }else{
//                 // GuardRail(sentence);
//                 Map<String, Object> otherResult = new HashMap<>();
//                 return otherResult;
//        }
    }


    private String formatDocumentGroup(String type, List<Document> documents, String searchQuery) {
        if (documents.isEmpty()) return "";

        StringBuilder sb = new StringBuilder();
        if (documents.size() == 1) {
            String score = ((Double)documents.getFirst().getMetadata().get("score")).toString();
            log.info("score for matching is : {}",score);
            int intPart = Integer.parseInt(score.split("\\.")[0]);
            String match = intPart >= 43 ? "exact" : "partial";
            sb.append(String.format(SINGLE_ENTRY_FORMAT, match, type, searchQuery));
        } else {
            sb.append(String.format(MULTIPLE_ENTRIES_FORMAT, documents.size(), type, searchQuery));
        }

        // Sort documents by score before displaying
        documents.sort((a, b) -> Double.compare(Double.valueOf(b.getMetadata().get("score").toString()), Double.valueOf(a.getMetadata().get("score").toString())));

        for (Document doc : documents) {
            Map<String, Object> metadata = doc.getMetadata();

            // Special formatting for Employee type OR when entity/filter indicates it's a person/user
            boolean isEmployeeType = "Employee".equalsIgnoreCase(type);
            boolean isPersonEntity = metadata.containsKey("entity") &&
                    ("user".equalsIgnoreCase(metadata.get("entity").toString()) ||
                            "person".equalsIgnoreCase(metadata.get("entity").toString()));
            boolean isEmployeeFilter = metadata.containsKey("filter") &&
                    "employee".equalsIgnoreCase(metadata.get("filter").toString());

            if (isEmployeeType || isPersonEntity || isEmployeeFilter) {
                // For employees/persons, try to get full name from metadata first
                String employeeName = doc.getText(); // Default to doc.getText()

                // Try to get full name from common metadata fields
                if (metadata.containsKey("name") && metadata.get("name") != null) {
                    employeeName = metadata.get("name").toString();
                } else if (metadata.containsKey("full_name") && metadata.get("full_name") != null) {
                    employeeName = metadata.get("full_name").toString();
                } else if (metadata.containsKey("employee_name") && metadata.get("employee_name") != null) {
                    employeeName = metadata.get("employee_name").toString();
                }

//                String userName = metadata.containsKey("userName") ? metadata.get("userName").toString() : "";
                String employeeId = metadata.containsKey("id") ? metadata.get("id").toString() : "";

                // Format as "name (ID - 78348)"
                if (!employeeId.isEmpty()) {
                    sb.append(String.format(ENTRY_DETAILS_FORMAT, employeeName + " (ID : " + employeeId + ")"));
                } else {
                    sb.append(String.format(ENTRY_DETAILS_FORMAT, employeeName));
                }

                // Add other employee-specific metadata if needed (excluding ID since it's now in the name line)
                if (metadata.containsKey("employee_code")) {
                    sb.append(String.format(METADATA_FORMAT, "Employee Code : ", metadata.get("employee_code")));
                }
                if (metadata.containsKey("designation")) {
                    sb.append(String.format(METADATA_FORMAT, "Designation : ", metadata.get("designation")));
                }
            } else {
                // Original formatting for non-employee types
//                sb.append(String.format(ENTRY_DETAILS_FORMAT, doc.getText()));

                // Add metadata based on type
                if ("Department".equalsIgnoreCase(type)) {
                    if (metadata.containsKey("code")) {
                        sb.append(String.format(METADATA_FORMAT, doc.getText(),"(ID : "+ metadata.get("code")+")"));
                    }
                } else if ("WorkGroup".equalsIgnoreCase(type)) {
                    if (metadata.containsKey("code")) {
                        sb.append(String.format(METADATA_FORMAT, doc.getText(), "(ID : "+ metadata.get("code")+")"));
                    }
                }else if ("project".equalsIgnoreCase(type)) {
                    if (metadata.containsKey("code")) {
                        sb.append(String.format(METADATA_FORMAT, doc.getText(), "(ID : "+ metadata.get("code")+")"));
                    }
                } else if ("Task".equalsIgnoreCase(type)) {
                    if (metadata.containsKey("id")) {
                        sb.append(String.format(METADATA_FORMAT, doc.getText(), "(ID : "+ metadata.get("id")+")"));
                    }
                } else if("Designation".equalsIgnoreCase(type)) {
                    if (metadata.containsKey("code")) {
                        sb.append(String.format(METADATA_FORMAT, doc.getText(), "(ID : "+ metadata.get("code")+")"));
                    }
                } else if("Country".equalsIgnoreCase(type)) {
                    if (metadata.containsKey("id")) {
                        sb.append(String.format(METADATA_FORMAT, doc.getText(), "(ID : "+ metadata.get("id")+")"));
                    }
                } else if("Location".equalsIgnoreCase(type)) {
                    if (metadata.containsKey("id")) {
                        sb.append(String.format(METADATA_FORMAT, doc.getText(), "(ID : "+ metadata.get("id")+")"));
                    }
                }
            }
        }

        sb.append("\n");
        return sb.toString();
    }


    /**
     * Filters documents to return only unique documents based on their ID in metadata.
     * If multiple documents have the same ID, keeps the one with the highest score.
     *
     * @param documents List of documents to filter
     * @return List of documents with unique IDs
     */
    private List<Document> filterUniqueDocuments(List<Document> documents) {
        if (documents == null || documents.isEmpty()) {
            return Collections.emptyList();
        }

        // Map to store unique documents by ID
        Map<String, Document> uniqueDocMap = new HashMap<>();

        for (Document doc : documents) {
            Object idObj = doc.getMetadata().get("id");
            String docId = idObj != null ? idObj.toString() : null;

            // Skip documents without ID
            if (docId == null || docId.isEmpty()) {
                log.warn("Document found without ID, skipping: {}", doc.getText());
                continue;
            }

            // If this ID is not in map yet, or if this document has a higher score, keep it
            if (!uniqueDocMap.containsKey(docId) ||
                    Double.valueOf(doc.getMetadata().get("score").toString()) > Double.valueOf(uniqueDocMap.get(docId).getMetadata().get("score").toString())) {

                if (uniqueDocMap.containsKey(docId)) {
                    log.debug("Replacing document with ID '{}' - new score: {}, old score: {}",
                            docId, Double.valueOf(doc.getMetadata().get("score").toString()), Double.valueOf(uniqueDocMap.get(docId).getMetadata().get("score").toString()));
                }

                uniqueDocMap.put(docId, doc);
            } else {
                log.debug("Skipping duplicate document with ID '{}' - score: {}, existing score: {}",
                        docId, Double.valueOf(doc.getMetadata().get("score").toString()), Double.valueOf(uniqueDocMap.get(docId).getMetadata().get("score").toString()));
            }
        }

        // Convert back to list and sort by score (highest first)
        List<Document> uniqueDocuments = new ArrayList<>(uniqueDocMap.values());
        uniqueDocuments.sort((a, b) -> Double.compare(Double.valueOf(b.getMetadata().get("score").toString()), Double.valueOf(a.getMetadata().get("score").toString())));

        log.info("Filtered {} unique documents from {} total documents", uniqueDocuments.size(), documents.size());
        return uniqueDocuments;
    }

    private FinalResponse getFinalDocuments(String query, ChatClientRequest request) {
        log.info("SYSTEM_CONTENT: {}", SYSTEM_CONTENT);
        List<Document> finalDocuments = new ArrayList<>();
        log.info("Starting getFinalDocuments with query: {}", query);
        long currentTime = System.currentTimeMillis();
        Map<String, Object> apiResponse = getApiResponse(query);
        log.info("Time taken huggingface API {}", System.currentTimeMillis() - currentTime);
        log.info("API Response received: {}", apiResponse);

        StringBuilder whoDetailsBuilder = new StringBuilder();
        StringBuilder whatDetailsBuilder = new StringBuilder();

        // Process who list
        List<String> whoList = (List<String>) apiResponse.get("who");
        log.info("Extracted who list: {}", whoList);

        String agentName = null;
        String token = "Bearer ";
        try {
            OpenAiChatOptions openAiChatOptions = (OpenAiChatOptions) request.prompt().getOptions();
            agentName = (String) openAiChatOptions.getToolContext().get("currentAgentName");
            String encodedToken = (String) openAiChatOptions.getToolContext().get("token");
            byte[] decodedBytes = Base64.getDecoder().decode(encodedToken);
            token = token + (new String(decodedBytes));
            log.info("agentName, token received from toolContext is : {}, {}", agentName,token);
        } catch (Exception e) {
            log.error("Error occured while fetching currentAgentName from toolContext : {}", e.getMessage(), e);
        }

        try{
            // Process WHO documents
            for (String who : whoList) {

                if (!checkIfEmailOrNot(who)) {
                    log.info("Processing who: {}", who);
                    long time = System.currentTimeMillis();
                    List<Document> documents = searchLucen(who, "employee", "", agentName, token);
                    log.info("Time taken lucene API {}", System.currentTimeMillis() - time);
//                List<Map<String, Object>> documents = luceneService.search(who,"Employee");
                    log.info("Found {} documents for {}", documents.size(), who);


                    if (!documents.isEmpty()) {
                        finalDocuments.addAll(documents);
                        whoDetailsBuilder.append(formatDocumentGroup("Employee", documents, who));
                    }
                    // else {
                    //     whoDetailsBuilder.append(String.format(NO_ENTRIES_FORMAT, "Employee", who));
                    // }
                } else {
                    log.info("Email address found in who String: {}", who);
                }
            }

            // Process what list
            List<Map<String, String>> whatMapList = (List<Map<String, String>>) apiResponse.get("what");
            log.info("Extracted what list: {}", whatMapList);

            if (whatMapList != null) {
                for (Map<String, String> whatMap : whatMapList) {


                    String what = whatMap.get("text");
                    if (!checkIfEmailOrNot(what)) {
                        String entityType = whatMap.getOrDefault("entity_type", "");
                        if (what == null || what.isEmpty()) continue;

                        log.info("Processing what: {}, entity_type: {}", what, entityType);
                        log.info("About to search for what: '{}', filter: '{}', entity_type: '{}'", what, "What", entityType);

                        List<Document> documents;
                        if ("person".equalsIgnoreCase(entityType)) {
                            documents = searchLucen(what, "employee", entityType, agentName, token);
                        } else if ("Document".equalsIgnoreCase(entityType) || "Documents".equalsIgnoreCase(entityType)) {
                            continue;
                        } else {
                            documents = searchLucen(what, "What", entityType, agentName, token);
                        }

                        if (!documents.isEmpty()) {
                            finalDocuments.addAll(documents);

                            // Group by type for this what entry, and print each group with this what as the searchQuery
                            Map<String, List<Document>> docsByType = new HashMap<>();
                            for (Document doc : documents) {
                                String docType = (String) doc.getMetadata().getOrDefault("type", "Unknown");
                                docsByType.computeIfAbsent(docType, k -> new ArrayList<>()).add(doc);
                            }
                            for (Map.Entry<String, List<Document>> entry : docsByType.entrySet()) {
                                String type = entry.getKey();
                                List<Document> docs = entry.getValue();
                                whatDetailsBuilder.append(formatDocumentGroup(type, docs, what));
                            }
                        }
                    } else {
                        log.info("Email address found in what String: {}", what);
                    }
                }
            }

            if (whoDetailsBuilder.toString().isEmpty() && whatDetailsBuilder.toString().isEmpty()) {
                System.out.println("Query -  " + query);
                log.info("Query -  " + query);
                return new FinalResponse("", "", finalDocuments);
            } else {
                // Build the final context information using DEFAULT_PROMPT_TEMPLATE
                String intentVariable = whoDetailsBuilder.toString() + whatDetailsBuilder.toString();
                String formattedOutput = this.promptTemplate.render(Map.of("query", query, "intent_variable", intentVariable));
                log.info("Loggers --> Formatted output: {}", formattedOutput);
                return new FinalResponse(formattedOutput, "", finalDocuments);
            }
        } catch (Exception e) {
            log.info("Exception occured while parsing api response : {}",e.getMessage(),e);
            return new FinalResponse(null,null,Collections.emptyList());
        }
    }

    private boolean checkIfEmailOrNot(String who) {
        if (who == null || who.trim().isEmpty()) {
            return false;
        }

        String emailRegex = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+$";
        return who.matches(emailRegex);
    }

    private List<Document> searchLucen(String searchText, String filter, String entity_type, String agentName, String token){
        try {

            if (searchText == null || searchText.isEmpty()) {
                log.warn("Search text is empty");
                return Collections.emptyList();
            }

            int limit = 15;
            log.debug("Starting Lucen search with text: '{}', filter: '{}', limit: {}", searchText, filter, limit);
            List<Document> searchResults = new ArrayList<>();
            log.info("After creating list of documents");

            // For employee searches, try different matching strategies
            if ("employee".equalsIgnoreCase(filter) && whoEnabler) {
                log.info("Inside If Block");

                List<Document> exactMatches = new ArrayList<>(sendGetForLuceneSearch(searchText, "Employee"));
                log.info("Found {} exact matches for query: '{}'", exactMatches.size(), searchText);

                if (!exactMatches.isEmpty()) {
                    searchResults = exactMatches;
                } else {

                    log.info("Inside @searchLuncen, no data found for employee");
                    searchResults = new ArrayList<>();
                }

                // Log details of found documents for debugging
                for (Document doc : searchResults) {
                    log.debug("Found document - Text: '{}', Metadata: {}", doc.getText(), doc.getMetadata());
                }

            }
            else if("What".equalsIgnoreCase(filter) && ("person".equalsIgnoreCase(entity_type)) && whatEnabler){
                List<Document> exactMatches = new ArrayList<>(sendGetForLuceneSearch(searchText, "Employee"));
                log.info("Found {} exact matches for query: '{}'", exactMatches.size(), searchText);

                if (!exactMatches.isEmpty()) {
                    searchResults = exactMatches;
                } else {
                    log.info("Inside @searchLuncen, no data found for employee");
                    searchResults = new ArrayList<>();
                }
            }
            else if(whatEnabler){
                // For non-employee searches, use standard search
                SearchRequest.Builder requestBuilder = SearchRequest.builder()
                        .query(searchText)
                        .similarityThreshold(partial);

                if (filter != null && !filter.isEmpty()) {
                    String filterExpression;
                    if ("project".equalsIgnoreCase(filter)) {
                        filterExpression = "(entity == 'project' || type == 'project') && filter == 'What'";
                        List<Document> exactMatches = new ArrayList<>(sendGetForLuceneSearch(searchText, "project"));
                        log.info("Found {} exact matches for query: '{}'", exactMatches.size(), searchText);
                    } else if ("Task".equalsIgnoreCase(filter)) {
                        filterExpression = "(entity == 'Task' || type == 'Task') && filter == 'What'";
                        List<Document> exactMatches = new ArrayList<>(sendGetForLuceneSearch(searchText, "Task"));
                        log.info("Found {} exact matches for query: '{}'", exactMatches.size(), searchText);
                    } else {
                        filterExpression = String.format("(filter == '%s' || entity == '%s')", filter, filter);
                    }
                    requestBuilder.filterExpression(filterExpression);
                }

                if("Workgroup".equalsIgnoreCase(entity_type)){
                    List<Document> exactMatches = new ArrayList<>(sendGetForLuceneSearch(searchText, "WorkGroup"));
                    log.info("In case of work group(directly), Found {} exact matches for query: '{}'", exactMatches.size(), searchText);
                    searchResults = exactMatches;
                } else if(("MeetingAssistant".equalsIgnoreCase(agentName) || "FileManagement".equalsIgnoreCase(agentName)) && "Department".equalsIgnoreCase(entity_type)){
                    List<Document> exactMatches = new ArrayList<>(sendGetForLuceneSearch(searchText, "WorkGroup"));
                    log.info("In case of work group(in directly), Found {} exact matches for query: '{}'", exactMatches.size(), searchText);
                    searchResults = exactMatches;
                }
                else if("Department".equalsIgnoreCase(entity_type)) {
                    List<Document> exactMatches = new ArrayList<>(sendGetForLuceneSearch(searchText, entity_type));
                    log.info("In case of department, Found {} exact matches for query: '{}'", exactMatches.size(), searchText);
                    searchResults = exactMatches;
                }else if("Country".equalsIgnoreCase(entity_type)) {
                    List<Document> exactMatches = new ArrayList<>(sendGetForLuceneSearch(searchText, "Country"));
                    log.info("In case of country, Found {} exact matches for query: '{}'", exactMatches.size(), searchText);
                    searchResults = exactMatches;
                } else if("Location".equalsIgnoreCase(entity_type) ) {
                    List<Document> exactMatches = new ArrayList<>(sendGetForLuceneSearch(searchText, "Location"));
                    log.info("In case of location, Found {} exact matches for query: '{}'", exactMatches.size(), searchText);
                    searchResults = exactMatches;
                } else if("TaskAssistant".equalsIgnoreCase(agentName) && ("Task".equalsIgnoreCase(entity_type) || "TodoTask".equalsIgnoreCase(entity_type))) {
//                    List<Document> exactMatches = new ArrayList<>(sendGetForLuceneSearch(searchText, "Task"));
                    List<Document> exactMatches = new ArrayList<>(sendGetForTaskSearch(searchText, "TASK_TITLE", token));
                    log.info("In case of task, Found {} exact matches for query: '{}'", exactMatches.size(), searchText);
                    searchResults = exactMatches;
                }

//                SearchRequest request = requestBuilder.build();
//                log.debug("Constructed search request: {}", request);
//                searchResults = new ArrayList<>(vectorStore.similaritySearch(request));
//                log.info("Found {} matches for query: '{}'", searchResults.size(), searchText);
//                to be skipped
            }
            else{
                log.info("Who and What are DISABLED");
                return Collections.emptyList();
            }

            // Filter out duplicate documents based on unique ID
            List<Document> uniqueDocuments = filterUniqueDocuments(searchResults);
            log.info("After filtering duplicates: {} unique documents out of {} total", uniqueDocuments.size(), searchResults.size());

            return uniqueDocuments;

        } catch (Exception e) {
            log.error("Error during Milvus search: {}", e.getMessage(), e);
            return Collections.emptyList();
        }



    }

    private List<Document> sendGetForLuceneSearch(String query, String type) {
        List<Document> documents = new ArrayList<>();

        try {
            String queryParam = URLEncoder.encode(query, StandardCharsets.UTF_8);
            String typeParam = URLEncoder.encode(type, StandardCharsets.UTF_8);
            String fullUrl = String.format("%s?query=%s&type=%s", LUCENE_URL, queryParam, typeParam);

            log.info("Calling Lucene API with URL: {}", fullUrl);

            URL url = new URL(fullUrl);
            HttpURLConnection con = (HttpURLConnection) url.openConnection();
            con.setRequestMethod("GET");
            con.setRequestProperty("Accept", "application/json");

            int status = con.getResponseCode();
            if (status != 200) {
                log.error("Lucene API responded with non-200 status: {}", status);
                return Collections.emptyList();
            }

            BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
            StringBuilder responseBuilder = new StringBuilder();
            String line;
            while ((line = in.readLine()) != null) {
                responseBuilder.append(line);
            }
            in.close();
            con.disconnect();

            String jsonResponse = responseBuilder.toString().trim();

            if (jsonResponse.isEmpty() || jsonResponse.equals("null")) {
                log.info("Lucene response is empty or null. Skipping parsing.");
                return Collections.emptyList();
            }

            log.debug("Lucene raw JSON response: {}", jsonResponse);

            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(jsonResponse);

            if (!rootNode.isArray()) {
                log.error("Expected JSON array from Lucene API but received: {}", rootNode.getNodeType());
                return Collections.emptyList();
            }

            for (JsonNode node : rootNode) {
                String text = node.hasNonNull("text") ? node.get("text").asText() : null;
                JsonNode mediaNode = node.get("media");
                boolean hasMedia = mediaNode != null && !mediaNode.isNull();

                // Must have exactly one: text or media
                if ((text == null && !hasMedia) || (text != null && hasMedia)) {
                    log.info("Skipping invalid document (text/media constraint violated): {}", node);
                    continue;
                }

                // Extract metadata
                Map<String, Object> metadata = new HashMap<>();
                if (node.has("metadata") && node.get("metadata").isObject()) {
                    node.get("metadata").fields().forEachRemaining(entry ->
                            metadata.put(entry.getKey(), mapper.convertValue(entry.getValue(), Object.class))
                    );
                }

                Document doc = new Document(text, metadata);
                documents.add(doc);
                log.debug("Parsed Document with text='{}' and metadata={}", text, metadata);
            }

        } catch (Exception e) {
            log.error("Error occurred while calling or parsing Lucene API response: {}", e.getMessage(), e);
        }

        log.info("Total documents parsed from Lucene: {}", documents.size());
        return documents;
    }

    public List<Document> sendGetForTaskSearch(String query, String title, String token) {
        List<Document> documents = new ArrayList<>();

        try {
            String queryParam = URLEncoder.encode(query, StandardCharsets.UTF_8);
            String titleParam = URLEncoder.encode(title, StandardCharsets.UTF_8);
            String fullUrl = String.format("%s?query=%s&title=%s", TASK_API_URL, queryParam, titleParam);

            log.info("Calling Task API with URL: {}", fullUrl);

            URL url = new URL(fullUrl);
            HttpURLConnection con = (HttpURLConnection) url.openConnection();
            con.setRequestMethod("GET");
            con.setRequestProperty("Content-Type", "application/json");
            con.setRequestProperty("Access-Token", token);

            int status = con.getResponseCode();
            if (status != 200) {
                log.error("Task API responded with non-200 status: {}", status);
                return Collections.emptyList();
            }

            BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
            StringBuilder responseBuilder = new StringBuilder();
            String line;
            while ((line = in.readLine()) != null) {
                responseBuilder.append(line);
            }
            in.close();
            con.disconnect();

            String jsonResponse = responseBuilder.toString().trim();

            if (jsonResponse.isEmpty() || jsonResponse.equals("null")) {
                log.info("Task response is empty or null. Skipping parsing.");
                return Collections.emptyList();
            }

            log.info("Task raw JSON response: {}", jsonResponse);

            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(jsonResponse);

            if (!rootNode.isArray()) {
                log.error("Expected JSON array from Task API but received: {}", rootNode.getNodeType());
                return Collections.emptyList();
            }

            for (JsonNode node : rootNode) {
                // Get taskTitle as text
                String text = node.hasNonNull("taskTitle") ? node.get("taskTitle").asText() : null;

                if (text == null || text.isEmpty()) {
                    log.info("Skipping task without taskTitle: {}", node);
                    continue;
                }

                // Create metadata map
                Map<String, Object> metadata = new HashMap<>();
                if (node.has("id")) {
                    metadata.put("id", node.get("id").asLong());
                }
                if (node.has("score")) {
                    metadata.put("score", node.get("score").asDouble());
                }
                if (node.has("type")) {
                    metadata.put("type", node.get("type").asText());
                }

                Document doc = new Document(text, metadata);
                documents.add(doc);
                log.info("Parsed Document with text='{}' and metadata={}", text, metadata);
            }

        } catch (Exception e) {
            log.error("Error occurred while calling or parsing Task API response: {}", e.getMessage(), e);
        }

        log.info("Total documents parsed from Task API: {}", documents.size());
        return documents;
    }



    public static final class Builder {
        private SearchRequest searchRequest = SearchRequest.builder().build();
        private PromptTemplate promptTemplate;
        private Scheduler scheduler;
        private int order = DEFAULT_ORDER;
        private double exact;
        private double partial;
        private Boolean whoEnabler;
        private Boolean whatEnabler;

        private Builder() {
        }

        public Builder promptTemplate(PromptTemplate promptTemplate) {
            Assert.notNull(promptTemplate, "promptTemplate cannot be null");
            this.promptTemplate = promptTemplate;
            return this;
        }

        public Builder searchRequest(SearchRequest searchRequest) {
            Assert.notNull(searchRequest, "The searchRequest must not be null!");
            this.searchRequest = searchRequest;
            return this;
        }

        public Builder protectFromBlocking(boolean protectFromBlocking) {
            this.scheduler = protectFromBlocking ? BaseAdvisor.DEFAULT_SCHEDULER : Schedulers.immediate();
            return this;
        }

        public Builder scheduler(Scheduler scheduler) {
            this.scheduler = scheduler;
            return this;
        }

        public Builder order(int order) {
            this.order = order;
            return this;
        }

        public Builder exact(double exact) {
            this.exact = exact;
            return this;
        }

        public Builder partial(double partial) {
            this.partial = partial;
            return this;
        }

        public Builder whoEnabler(Boolean whoEnabler){
            this.whoEnabler=whoEnabler;
            return this;
        }

        public Builder whatEnabler(Boolean whatEnabler){
            this.whatEnabler=whatEnabler;
            return this;
        }

        public IntentEnhanceAdvisor build() {
            return new IntentEnhanceAdvisor(this.searchRequest, this.promptTemplate, this.scheduler,
                    this.order, this.exact, this.partial, this.whoEnabler, this.whatEnabler);
        }
    }
}
